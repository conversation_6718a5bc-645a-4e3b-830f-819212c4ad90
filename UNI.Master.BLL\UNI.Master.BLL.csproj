<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>UNI.Master.BLL</AssemblyName>
    <PackageId>UNI.Master.BLL</PackageId>    
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <LangVersion>latest</LangVersion>
    <RootNamespace>UNI.Master.BLL</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Interfaces\INotificationService.cs" />
    <Compile Remove="Interfaces\IProductPackageDetailService.cs" />
    <Compile Remove="Interfaces\IProductPackageMenuService.cs" />
    <Compile Remove="BusinessService\NotificationService.cs" />
    <Compile Remove="BusinessService\ProductPackageDetailService.cs" />
    <Compile Remove="BusinessService\ProductPackageMenuService.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="KubernetesClient" Version="16.0.2" />
	  <PackageReference Include="Minio" Version="6.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\uni-common\UNI.Utilities.StringExtension\UNI.Utilities.StringExtension.csproj" />
    <ProjectReference Include="..\UNI.Master.DAL\UNI.Master.DAL.csproj" />
    <ProjectReference Include="..\UNI.Master.HarborClient\UNI.Master.HarborClient.csproj" />
    <ProjectReference Include="..\Uni.Master.K8s.Extension\Uni.Master.K8s.Extension.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Interfaces\HarborRegistry\" />
  </ItemGroup>

</Project>
