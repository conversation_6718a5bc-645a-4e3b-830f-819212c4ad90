using System;
using System.Threading.Tasks;
using UNI.Master.Model.Database;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces.Database
{
    public interface IDatabaseInstanceService
    {
        Task<CommonListPage> GetPageAsync(DatabaseInstanceFilter filter);
        Task<DatabaseInstanceInfo> GetInfoAsync(Guid? id);
        Task<BaseValidate<Guid?>> SetInfoAsync(DatabaseInstanceInfo info);
        Task<BaseValidate> DeleteAsync(Guid? id);
    }
}
