<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AssemblyName>UNI.Master.DAL</AssemblyName>
    <PackageId>UNI.Master.DAL</PackageId>    
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <LangVersion>latest</LangVersion>
    <RootNamespace>UNI.Master.DAL</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Interfaces\INotificationRepository.cs" />
    <Compile Remove="Interfaces\IProductPackageDetailRepository.cs" />
    <Compile Remove="Interfaces\IProductPackageMenuRepository.cs" />
    <Compile Remove="Repositories\NotificationRepository.cs" />
    <Compile Remove="Repositories\ProductPackageDetailRepository.cs" />
    <Compile Remove="Repositories\ProductPackageMenuRepository.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Repositories\SHousingRepository.cs~RF3a78c445.TMP" />
  </ItemGroup>

  <ItemGroup>
    <!--<PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="*********" />-->
    <!--<PackageReference Include="BarcodeLib" Version="2.2.6" />-->
    <!--<PackageReference Include="Dapper" Version="2.1.28" />-->
    <!--<PackageReference Include="Elasticsearch.Net" Version="7.5.1" />
    <PackageReference Include="Elasticsearch.Net.Aws" Version="2.4.0" />
    <PackageReference Include="FirebaseAuthentication.net" Version="3.4.0" />
    <PackageReference Include="FirebaseDatabase.net" Version="4.0.4" />-->
    <!--<PackageReference Include="Microsoft.AspNetCore.Authentication.Cookies" Version="2.2.0" />-->
    <!--<PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.0.2" />-->
    <!--<PackageReference Include="IdentityServer4.AccessTokenValidation" Version="2.6.0" />-->
    <!--<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="2.0.2" />-->
    <!--<PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />-->
    <!--<PackageReference Include="NEST" Version="7.5.1" />-->
    <!--<PackageReference Include="Npgsql" Version="6.0.3" />-->
    <!--<PackageReference Include="Oracle.ManagedDataAccess.Core" Version="2.12.0-beta2" />-->
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\uni-common\UNI.Common\UNI.Common.csproj" />
    <ProjectReference Include="..\UNI.Master.DapperExtension\UNI.Master.DapperExtension.csproj" />
    <ProjectReference Include="..\UNI.Master.HarborClient\UNI.Master.HarborClient.csproj" />
    <ProjectReference Include="..\UNI.Master.Model\UNI.Master.Model.csproj" />
  </ItemGroup>

</Project>
