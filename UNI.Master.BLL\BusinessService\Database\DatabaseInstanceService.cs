using System;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Database;
using UNI.Master.DAL.Interfaces.Database;
using UNI.Master.Model.Database;
using UNI.Model;

namespace UNI.Master.BLL.BusinessService.Database
{
    public class DatabaseInstanceService : IDatabaseInstanceService
    {
        private readonly IDatabaseInstanceRepository _repository;

        public DatabaseInstanceService(IDatabaseInstanceRepository repository)
        {
            _repository = repository;
        }

        public Task<CommonListPage> GetPageAsync(DatabaseInstanceFilter filter)
        {
            return _repository.GetPageAsync(filter);
        }

        public Task<DatabaseInstanceInfo> GetInfoAsync(Guid? id)
        {
            return _repository.GetInfoAsync(id);
        }

        public Task<BaseValidate<Guid?>> SetInfoAsync(DatabaseInstanceInfo info)
        {
            return _repository.SetInfoAsync(info);
        }

        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            return _repository.DeleteAsync(id);
        }
    }
}
