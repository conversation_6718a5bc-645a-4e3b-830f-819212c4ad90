using AutoMapper;
using k8s.Models;
using UNI.Master.Model.Deployments;

namespace UNI.Master.BLL.MapperProfile
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            //CreateMap<CustomerProductDto, CustomerProduct>().ReverseMap();
            //CreateMap<DatabaseInstanceDto, Dbinstance>().ReverseMap();
            CreateMap<V1Namespace, DeploymentTenantModel>()
                .ForMember(d => d.name, o => o.MapFrom(s => s.Metadata.Name))
                .ForMember(d => d.status, o => o.MapFrom(s => s.Status.Phase))
                .ForMember(d => d.NameSpace, o => o.MapFrom(s => s.Namespace()));

            CreateMap<ProjectModel, HarborClient.Models.Projects.ProjectModel>().ReverseMap();
            CreateMap<RepositoryModel, HarborClient.Models.Repositories.RepositoryModel>().ReverseMap();
            CreateMap<ArtifactModel, HarborClient.Models.Artifacts.ArtifactModel>().ReverseMap();
            CreateMap<TagModel, HarborClient.Models.Artifacts.TagModel>().ReverseMap();
        }
    }
}
